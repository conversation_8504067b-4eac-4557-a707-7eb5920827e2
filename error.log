100 epochs completed in 0.391 hours.
Optimizer stripped from runs/detect/train/weights/last.pt, 22.5MB
Optimizer stripped from runs/detect/train/weights/best.pt, 22.5MB

Validating runs/detect/train/weights/best.pt...
Ultralytics 8.3.28  Python-3.8.16 torch-1.13.0+cu116 CUDA:0 (NVIDIA GeForce RTX 4090, 24217MiB)
Model summary (fused): 168 layers, 11,125,971 parameters, 0 gradients, 28.4 GFLOPs
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:01<00:00,  8.90it/s]
                   all        365        364      0.662      0.661        0.7      0.443
Speed: 0.1ms preprocess, 1.2ms inference, 0.0ms loss, 0.2ms postprocess per image
Results saved to mruns/detect/train

0: 640x640 1 drug-name, 4.2ms
Speed: 0.6ms preprocess, 4.2ms inference, 0.4ms postprocess per image at shape (1, 3, 640, 640)