# import netron
import torch
from PIL import Image
import onnx
import sys
import os
import numpy as np
from pathlib import Path
from typing import Union
import cv2
from ultralytics import YOL<PERSON>

def train():
    # 加载模型配置文件，重头开始训练
    # model = YOLO('yolov8s.yaml')

    # 使用预训练模型进行训练
    model = YOLO('weights/yolov8s.pt')
    # model = YOLO('yolov8n.yaml').load('yolov8n.pt')

    # 使用medicine数据集训练模型
    model.train(data="medicine.yaml", epochs=100, imgsz=640, workers=0)

def onnx():
    # 使用onnx导出文件
    # model = YOLO('yolov8n.pt')  # load an official model
    model = YOLO('runs/detect/train/weights/best.pt')  # load a custom trained
    # Export the model
    model.export(format='onnx')

def test_img():
	# 训练好的模型权重路径
    model = YOLO("runs/detect/train/weights/best.pt")
    # 测试图片的路径
    img = cv2.imread("test.jpg")
    res = model(img)
    ann = res[0].plot()
    while True:
        cv2.imshow("yolo", ann)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    # 设置保存图片的路径
    cur_path = sys.path[0]
    print(cur_path, sys.path)

    if os.path.exists(cur_path):
        cv2.imwrite(cur_path + "out.jpg", ann)
    else:
        os.mkdir(cur_path)
        cv2.imwrite(cur_path + "out.jpg", ann)

def predict():
    # Load a model
    # model = YOLO('yolov8n.pt')  # 加载官方的模型权重作评估
    model = YOLO('runs/detect/train/weights/best.pt')  # 加载自定义的模型权重作评估

    # 评估
    metrics = model.val(workers=0)
    # 如果要在新的数据集上测试训练结果，需要将数据集绝对路径传入，例如：
    # metrics = model.val(data=“YOLOv8/.../VOC.yaml”)
    print(metrics.box.map)  # map50-95
    print(metrics.box.map50)  # map50
    print(metrics.box.map75)  # map75
    print(metrics.box.maps)  # 包含每个类别的map50-95列表

    # Accessing AP75 for each category
    ap75_each_category = metrics.box.maps[:, 5]  # 利用maps矩阵可以得到AP75
    print(ap75_each_category)


train()
test_img()
predict()
# onnx()